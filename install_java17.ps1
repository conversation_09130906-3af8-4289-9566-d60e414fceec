# Script para descargar e instalar Java 17
$downloadUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.10%2B7/OpenJDK17U-jdk_x64_windows_hotspot_17.0.10_7.msi"
$installerPath = "$env:TEMP\jdk17_installer.msi"

Write-Host "Descargando Java 17..."
Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath

Write-Host "Instalando Java 17..."
Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $installerPath, "/quiet", "/qn", "/norestart" -Wait

Write-Host "Verificando la instalación..."
$javaHome = "C:\Program Files\Java\jdk-17"
if (Test-Path $javaHome) {
    Write-Host "Java 17 se ha instalado correctamente en $javaHome"
} else {
    Write-Host "No se pudo encontrar la instalación de Java 17 en la ubicación esperada."
    Write-Host "Por favor, verifica manualmente la instalación."
}

Write-Host "Limpiando archivos temporales..."
Remove-Item $installerPath -Force

Write-Host "Instalación completada. Por favor, reinicia tu IDE para aplicar los cambios."
