import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mila_tour_app/core/constants/app_colors.dart';
import 'package:mila_tour_app/presentation/facilities/controller/facilities_controller.dart';
import 'package:mila_tour_app/presentation/facilities/view/facility_detail_screen.dart';
import 'package:mila_tour_app/presentation/facilities/widgets/facility_card.dart';

class FacilitiesGridScreen extends StatelessWidget {
  final String facilityType;
  final String title;

  const FacilitiesGridScreen({
    Key? key,
    required this.facilityType,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Obtener o crear el controlador
    final FacilitiesController controller = Get.put(FacilitiesController());
    
    // Cargar los datos al construir la pantalla
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadFacilities(facilityType);
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 60,
                ),
                const SizedBox(height: 16),
                Text(
                  controller.error.value,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.loadFacilities(facilityType),
                  child: const Text('Reintentar'),
                ),
              ],
            ),
          );
        }

        if (controller.getItemCount() == 0) {
          return const Center(
            child: Text('No hay elementos disponibles'),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: controller.getItemCount(),
            itemBuilder: (context, index) {
              final item = controller.getItem(index);
              return FacilityCard(
                title: item.name,
                rating: item.rating,
                // Usar una imagen predeterminada o la primera imagen si está disponible
                imageUrl: _getImageUrl(item),
                onTap: () {
                  Get.to(
                    () => FacilityDetailScreen(
                      facility: item,
                      facilityType: facilityType,
                    ),
                  );
                },
              );
            },
          ),
        );
      }),
    );
  }

  String _getImageUrl(dynamic item) {
    // Verificar si el item tiene una propiedad 'images' y si tiene elementos
    if (item is dynamic && 
        item.toString().contains('images') && 
        item.images is List && 
        item.images.isNotEmpty) {
      return item.images[0];
    }
    
    // Imágenes predeterminadas según el tipo de instalación
    switch (facilityType) {
      case 'associations':
        return 'https://via.placeholder.com/150?text=Association';
      case 'auberge':
        return 'https://via.placeholder.com/150?text=Auberge';
      case 'hammam':
        return 'https://via.placeholder.com/150?text=Hammam';
      case 'hotel':
        return 'https://via.placeholder.com/150?text=Hotel';
      case 'manbaa':
        return 'https://via.placeholder.com/150?text=Manbaa';
      case 'tourist_attraction':
        return 'https://via.placeholder.com/150?text=Tourist+Attraction';
      case 'traditional':
        return 'https://via.placeholder.com/150?text=Traditional';
      case 'travel_agency':
        return 'https://via.placeholder.com/150?text=Travel+Agency';
      default:
        return 'https://via.placeholder.com/150?text=Facility';
    }
  }
}
