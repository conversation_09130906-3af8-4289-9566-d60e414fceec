import 'package:http/http.dart' as http;
import 'package:mila_tour_app/core/api/api_config.dart';

class AssociationsApiService {
  Future<String> getAssociations() async {
    final response = await http.get(Uri.parse(ApiConfig.getAssociations));
    if (response.statusCode == 200) {
      return response.body;
    } else {
      throw Exception('Failed to load associations');
    }
  }

  /*
  Future<String> getAssociationById(int id) async {
    final response = await http.get(
      Uri.parse('${ApiConfig.getAssociationById}/$id'),
    );
    if (response.statusCode == 200) {
      return response.body;
    } else {
      throw Exception('Failed to load association');
    }
  }

  Future<String> createAssociation(dynamic association) async {
    final response = await http.post(
      Uri.parse(ApiConfig.createAssociation),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: association.toJson(),
    );
    if (response.statusCode == 201) {
      return response.body;
    } else {
      throw Exception('Failed to create association');
    }
  }

  Future<String> updateAssociation(dynamic association) async {
    final response = await http.put(
      Uri.parse('${ApiConfig.updateAssociation}/${association.id}'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: association.toJson(),
    );
    if (response.statusCode == 200) {
      return response.body;
    } else {
      throw Exception('Failed to update association');
    }
  }

  Future<void> deleteAssociation(int id) async {
    final response = await http.delete(
      Uri.parse('${ApiConfig.deleteAssociation}/$id'),
    );
    if (response.statusCode != 204) {
      throw Exception('Failed to delete association');
    }
  }
  */
}
