import 'dart:convert';
import '../models/api_response.dart'; // Import the new ApiResponse model
import '../models/user.dart';
import '../services/user_service.dart';

class UserRepository {
  final UserApiService userApiService;

  UserRepository({required this.userApiService});

  Future<List<User>> getUsers() async {
    try {
      final jsonResponse = jsonDecode(
        (await userApiService.getUsers()).toString(),
      );
      final data = jsonResponse['data'] as List;
      return data.map((item) => User.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get users: $e');
    }
  }

  Future<ApiResponse<User>> login(String email, String password) async {
    try {
      final responseBody = await userApiService.login(email, password);
      final jsonResponse = jsonDecode(responseBody);
      if (jsonResponse['response'] == true) {
        // Assuming the 'data' field contains the user object
        if (jsonResponse['data'] != null) {
          return ApiResponse(
            success: true,
            data: User.fromJson(jsonResponse['data']),
            message:
                jsonResponse['message'], // Include message even on success if available
          );
        } else {
          // Login reported success, but no user data
          return ApiResponse(
            success: false,
            message: 'Login successful but no user data received.',
          );
        }
      } else {
        // Login failed according to the API
        return ApiResponse(
          success: false,
          message: jsonResponse['message'] ?? 'Login failed: Unknown error',
        );
      }
    } catch (e) {
      // Handle network/parsing errors
      return ApiResponse(
        success: false,
        message: 'Failed to login: An unexpected error occurred. $e',
      );
    }
  }
}
