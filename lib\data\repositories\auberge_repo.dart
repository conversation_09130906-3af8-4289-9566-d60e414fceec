import 'dart:convert';
import '../models/auberge.dart';
import '../services/auberge_service.dart';

class AubergeRepository {
  final AubergeApiService aubergeApiService;

  AubergeRepository({required this.aubergeApiService});

  Future<List<Auberge>> getAuberges() async {
    try {
      final jsonResponse = jsonDecode(
        (await aubergeApiService.getAuberges()).toString(),
      );
      final data = jsonResponse['data'] as List;
      return data.map((item) => Auberge.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get auberges: $e');
    }
  }
}
