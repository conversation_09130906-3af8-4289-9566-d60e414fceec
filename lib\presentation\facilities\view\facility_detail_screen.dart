import 'package:flutter/material.dart';
import 'package:mila_tour_app/core/constants/app_colors.dart';

class FacilityDetailScreen extends StatelessWidget {
  final dynamic facility;
  final String facilityType;

  const FacilityDetailScreen({
    Key? key,
    required this.facility,
    required this.facilityType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(facility.name),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main image or image carousel
            _buildImageSection(),

            // General information
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and rating
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          facility.name,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (facility.rating != null)
                        _buildRatingWidget(facility.rating),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Specific details by facility type
                  ..._buildSpecificDetails(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    // Check if facility has images
    if (facility.toString().contains('images') &&
        facility.images is List &&
        facility.images.isNotEmpty) {
      return SizedBox(
        height: 250,
        child: PageView.builder(
          itemCount: facility.images.length,
          itemBuilder: (context, index) {
            return Image.network(
              facility.images[index],
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(Icons.image_not_supported, size: 50),
                  ),
                );
              },
            );
          },
        ),
      );
    } else {
      // Default image when no images available
      return Container(
        height: 250,
        color: Colors.grey[300],
        child: Center(
          child: Icon(
            _getIconForFacilityType(),
            size: 80,
            color: Colors.grey[600],
          ),
        ),
      );
    }
  }

  IconData _getIconForFacilityType() {
    switch (facilityType) {
      case 'associations':
        return Icons.groups;
      case 'auberge':
        return Icons.cabin;
      case 'hammam':
        return Icons.hot_tub;
      case 'hotel':
        return Icons.hotel;
      case 'manbaa':
        return Icons.water_drop;
      case 'tourist_attraction':
        return Icons.attractions;
      case 'traditional':
        return Icons.home_work;
      case 'travel_agency':
        return Icons.card_travel;
      default:
        return Icons.place;
    }
  }

  Widget _buildRatingWidget(int? rating) {
    if (rating == null) return const SizedBox.shrink();

    return Row(
      children: [
        ...List.generate(
          5,
          (index) => Icon(
            index < rating ? Icons.star : Icons.star_border,
            color: Colors.amber,
            size: 20,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$rating/5',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  List<Widget> _buildSpecificDetails() {
    final List<Widget> details = [];

    switch (facilityType) {
      case 'associations':
        if (facility.ownerName != null) {
          details.add(_buildDetailItem('Owner', facility.ownerName));
        }
        if (facility.address != null) {
          details.add(_buildDetailItem('Address', facility.address));
        }
        if (facility.telephone != null) {
          details.add(_buildDetailItem('Phone', facility.telephone));
        }
        break;

      case 'auberge':
        if (facility.address != null) {
          details.add(_buildDetailItem('Address', facility.address));
        }
        if (facility.bedNumber != null) {
          details.add(
            _buildDetailItem('Number of beds', '${facility.bedNumber}'),
          );
        }
        break;

      case 'hammam':
        if (facility.ownerName != null) {
          details.add(_buildDetailItem('Owner', facility.ownerName));
        }
        break;

      case 'hotel':
        if (facility.address != null) {
          details.add(_buildDetailItem('Address', facility.address));
        }
        if (facility.theme != null) {
          details.add(_buildDetailItem('Theme', facility.theme));
        }
        if (facility.telephone != null) {
          details.add(_buildDetailItem('Phone', facility.telephone));
        }
        if (facility.fax != null) {
          details.add(_buildDetailItem('Fax', facility.fax));
        }
        break;

      case 'manbaa':
        if (facility.city != null) {
          details.add(_buildDetailItem('City', facility.city));
        }
        if (facility.flow != null) {
          details.add(_buildDetailItem('Flow rate', '${facility.flow} L/s'));
        }
        if (facility.temperature != null) {
          details.add(
            _buildDetailItem('Temperature', '${facility.temperature}°C'),
          );
        }
        break;

      case 'tourist_attraction':
      case 'traditional':
        if (facility.description != null) {
          details.add(_buildDetailItem('Description', facility.description));
        }
        break;

      case 'travel_agency':
        if (facility.ownerName != null) {
          details.add(_buildDetailItem('Owner', facility.ownerName));
        }
        if (facility.managerName != null) {
          details.add(_buildDetailItem('Manager', facility.managerName));
        }
        if (facility.address != null) {
          details.add(_buildDetailItem('Address', facility.address));
        }
        if (facility.fax != null) {
          details.add(_buildDetailItem('Fax', facility.fax));
        }
        break;
    }

    return details;
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.secondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(value, style: const TextStyle(fontSize: 16)),
          const Divider(),
        ],
      ),
    );
  }
}
