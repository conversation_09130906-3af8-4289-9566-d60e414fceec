import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:mila_tour_app/presentation/facilities/controller/facilities_controller.dart';
import 'core/di/dependency_injection.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await DependencyInjection.init();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});
  FacilitiesController facilitiesController = Get.put(FacilitiesController());

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: ElevatedButton(
            child: const Text('test'),
            onPressed: () async {
              facilitiesController.loadFacilities('hotel');
              for (var hotel in facilitiesController.hotels) {
                print(hotel.name);
              }
            },
          ),
        ),
      ),
    );
  }
}
