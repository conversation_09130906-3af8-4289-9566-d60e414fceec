import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mila_tour_app/core/routes/app_routes.dart';

class TransportOptionsGrid extends StatelessWidget {
  const TransportOptionsGrid({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: const Color(0xFF00C160), // Color primario de la aplicación
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: GridView.count(
        crossAxisCount: 4, // Cambiado a 4 columnas para mejor visualización
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        mainAxisSpacing: 15,
        crossAxisSpacing: 15,
        children: [
          // Nuevas categorías solicitadas con iconos apropiados
          _buildTransportOption(context, Icons.groups, 'associations'),
          _buildTransportOption(context, Icons.cabin, 'auberge'),
          _buildTransportOption(context, Icons.hot_tub, 'hammam'),
          _buildTransportOption(context, Icons.hotel, 'hotel'),
          _buildTransportOption(context, Icons.water_drop, 'manbaa'),
          _buildTransportOption(
            context,
            Icons.attractions,
            'tourist_attraction',
          ),
          _buildTransportOption(context, Icons.home_work, 'traditional'),
          _buildTransportOption(context, Icons.card_travel, 'travel_agency'),
        ],
      ),
    );
  }

  Widget _buildTransportOption(
    BuildContext context,
    IconData icon,
    String label, {
    int? badgeCount,
  }) {
    return InkWell(
      onTap: () {
        // Navegar a la pantalla de instalaciones correspondiente
        Get.toNamed('${AppRoutes.facilities}/$label');
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(51), // 0.2 opacity
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withAlpha(77),
            width: 1,
          ), // 0.3 opacity
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(icon, color: Colors.white, size: 32),
                if (badgeCount != null)
                  Positioned(
                    top: -5,
                    right: -5,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$badgeCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
