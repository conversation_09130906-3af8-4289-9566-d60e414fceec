import 'package:get/get.dart';
import 'package:mila_tour_app/data/models/associations.dart';
import 'package:mila_tour_app/data/models/auberge.dart';
import 'package:mila_tour_app/data/models/hammam.dart';
import 'package:mila_tour_app/data/models/hotel.dart';
import 'package:mila_tour_app/data/models/manbaa.dart';
import 'package:mila_tour_app/data/models/tourist_attraction.dart';
import 'package:mila_tour_app/data/models/traditional.dart';
import 'package:mila_tour_app/data/models/travel_agency.dart';
import 'package:mila_tour_app/data/repositories/associations_repo.dart';
import 'package:mila_tour_app/data/repositories/auberge_repo.dart';
import 'package:mila_tour_app/data/repositories/hammam_repo.dart';
import 'package:mila_tour_app/data/repositories/hotel_repo.dart';
import 'package:mila_tour_app/data/repositories/manbaa_repo.dart';
import 'package:mila_tour_app/data/repositories/tourist_attraction_repo.dart';
import 'package:mila_tour_app/data/repositories/traditional_repo.dart';
import 'package:mila_tour_app/data/repositories/travel_agency_repo.dart';
import 'package:mila_tour_app/data/services/associations_service.dart';
import 'package:mila_tour_app/data/services/auberge_service.dart';
import 'package:mila_tour_app/data/services/hammam_service.dart';
import 'package:mila_tour_app/data/services/hotel_service.dart';
import 'package:mila_tour_app/data/services/manbaa_service.dart';
import 'package:mila_tour_app/data/services/tourist_attraction_service.dart';
import 'package:mila_tour_app/data/services/traditional_service.dart';
import 'package:mila_tour_app/data/services/travel_agency_service.dart';

class FacilitiesController extends GetxController {
  // Observables para cada tipo de instalación
  final RxList<Associations> associations = <Associations>[].obs;
  final RxList<Auberge> auberges = <Auberge>[].obs;
  final RxList<Hammam> hammams = <Hammam>[].obs;
  final RxList<Hotel> hotels = <Hotel>[].obs;
  final RxList<Manbaa> manbaas = <Manbaa>[].obs;
  final RxList<TouristAttraction> touristAttractions =
      <TouristAttraction>[].obs;
  final RxList<Traditional> traditionals = <Traditional>[].obs;
  final RxList<TravelAgency> travelAgencies = <TravelAgency>[].obs;

  // Estado de carga
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString currentFacilityType = ''.obs;

  // Repositorios
  late AssociationsRepository _associationsRepository;
  late AubergeRepository _aubergeRepository;
  late HammamRepository _hammamRepository;
  late HotelRepository _hotelRepository;
  late ManbaaRepository _manbaaRepository;
  late TouristAttractionRepository _touristAttractionRepository;
  late TraditionalRepository _traditionalRepository;
  late TravelAgencyRepository _travelAgencyRepository;

  @override
  void onInit() {
    super.onInit();
    _initRepositories();
  }

  void _initRepositories() {
    // Inicializar servicios
    final associationsService = AssociationsApiService();
    final aubergeService = AubergeApiService();
    final hammamService = HammamApiService();
    final hotelService = HotelApiService();
    final manbaaService = ManbaaApiService();
    final touristAttractionService = TouristAttractionApiService();
    final traditionalService = TraditionalApiService();
    final travelAgencyService = TravelAgencyApiService();

    // Inicializar repositorios
    _associationsRepository = AssociationsRepository(
      associationsApiService: associationsService,
    );
    _aubergeRepository = AubergeRepository(aubergeApiService: aubergeService);
    _hammamRepository = HammamRepository(hammamApiService: hammamService);
    _hotelRepository = HotelRepository(hotelApiService: hotelService);
    _manbaaRepository = ManbaaRepository(manbaaApiService: manbaaService);
    _touristAttractionRepository = TouristAttractionRepository(
      touristAttractionApiService: touristAttractionService,
    );
    _traditionalRepository = TraditionalRepository(
      traditionalApiService: traditionalService,
    );
    _travelAgencyRepository = TravelAgencyRepository(
      travelAgencyApiService: travelAgencyService,
    );
  }

  // Método para cargar instalaciones según el tipo
  Future<void> loadFacilities(String facilityType) async {
    isLoading.value = true;
    error.value = '';
    currentFacilityType.value = facilityType;

    try {
      switch (facilityType) {
        case 'auberge':
          auberges.value = await _aubergeRepository.getAuberges();
          break;
        case 'hammam':
          hammams.value = await _hammamRepository.getHammams();
          break;
        case 'hotel':
          hotels.value = await _hotelRepository.getHotels();
          break;
        case 'manbaa':
          manbaas.value = await _manbaaRepository.getManbaas();
          break;
        case 'tourist_attraction':
          touristAttractions.value =
              await _touristAttractionRepository.getTouristAttractions();
          break;
        case 'traditional':
          traditionals.value = await _traditionalRepository.getTraditionals();
          break;
        case 'travel_agency':
          travelAgencies.value =
              await _travelAgencyRepository.getTravelAgencies();
          break;
        default:
          error.value = 'Installation type not recognized';
      }
    } catch (e) {
      error.value = 'Error loading data: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Método para obtener el número de elementos según el tipo
  int getItemCount() {
    switch (currentFacilityType.value) {
      case 'associations':
        return associations.length;
      case 'auberge':
        return auberges.length;
      case 'hammam':
        return hammams.length;
      case 'hotel':
        return hotels.length;
      case 'manbaa':
        return manbaas.length;
      case 'tourist_attraction':
        return touristAttractions.length;
      case 'traditional':
        return traditionals.length;
      case 'travel_agency':
        return travelAgencies.length;
      default:
        return 0;
    }
  }

  // Método para obtener un elemento específico según el tipo e índice
  dynamic getItem(int index) {
    switch (currentFacilityType.value) {
      case 'associations':
        return associations[index];
      case 'auberge':
        return auberges[index];
      case 'hammam':
        return hammams[index];
      case 'hotel':
        return hotels[index];
      case 'manbaa':
        return manbaas[index];
      case 'tourist_attraction':
        return touristAttractions[index];
      case 'traditional':
        return traditionals[index];
      case 'travel_agency':
        return travelAgencies[index];
      default:
        return null;
    }
  }
}
