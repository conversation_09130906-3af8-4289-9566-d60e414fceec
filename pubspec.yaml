name: mila_tour_app
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.0-91.0.dev # Using the provided SDK constraint

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  http: ^1.3.0
  meta: ^1.16.0
  get: ^4.6.5 # Kept for GetX state management
  dartz: ^0.10.1
  logger: ^2.5.0
  flutter_map: ^7.0.1 # Added for map functionality
  latlong2: ^0.9.1 # Added for map coordinates
  image_picker: ^0.8.7
  cloudinary_public: ^0.23.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0 # Kept the provided version

flutter:
  uses-material-design: true
  assets:
    - assets/logo.jpg
    # - assets/images/a_dot_ham.jpeg
    # To add assets to your application, add an assets section, like this:
    # assets:
    #   - images/a_dot_burr.jpeg
    #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages