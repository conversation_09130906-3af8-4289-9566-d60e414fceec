import 'dart:convert';
import '../models/hotel.dart';
import '../services/hotel_service.dart';

class HotelRepository {
  final HotelApiService hotelApiService;

  HotelRepository({required this.hotelApiService});

  Future<List<Hotel>> getHotels() async {
    try {
      final jsonResponse = jsonDecode(await hotelApiService.getHotels());
      final data = jsonResponse['data'] as List;
      return data.map((item) => Hotel.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get hotels: $e');
    }
  }
}
