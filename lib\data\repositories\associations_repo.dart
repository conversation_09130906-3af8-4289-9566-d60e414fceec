import 'dart:convert';
import '../models/associations.dart';
import '../services/associations_service.dart';

class AssociationsRepository {
  final AssociationsApiService associationsApiService;

  AssociationsRepository({required this.associationsApiService});

  Future<List<Associations>> getAssociations() async {
    try {
      final jsonResponse = jsonDecode(
        (await associationsApiService.getAssociations()).toString(),
      );
      print("owiiiiiiiiiiiiiiiiiiiiiii");
      print(jsonResponse['data']);
      final data = jsonResponse['data'] as List;
      return data.map((item) => Associations.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get associations: $e');
    }
  }
}
