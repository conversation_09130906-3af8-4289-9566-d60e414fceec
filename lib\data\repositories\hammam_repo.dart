import 'dart:convert';
import '../models/hammam.dart';
import '../services/hammam_service.dart';

class HammamRepository {
  final HammamApiService hammamApiService;

  HammamRepository({required this.hammamApiService});

  Future<List<Hammam>> getHammams() async {
    try {
      final jsonResponse = jsonDecode(await hammamApiService.getHammams());

      final data = jsonResponse['data'] as List;
      return data.map((item) => Hammam.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get hammams: $e');
    }
  }
}
